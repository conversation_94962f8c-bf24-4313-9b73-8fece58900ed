package controller

import (
	"context"
	"fmt"
	"new-gitlab.xunlei.cn/vcproject/backends/svcreview/model"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/review"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/review/shumei"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcreview"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
	"new-gitlab.xunlei.cn/vcproject/backends/svcreview/services"
)

var (
	_ svcreview.SServer = &Controller{}
)

type Controller struct {
	r       ReviewCallback
	metrics *services.ReviewMetricsService
}

func NewController() *Controller {
	svcmgr.InitServices()
	return &Controller{
		r:       NewDefaultReviewCallback(),
		metrics: services.NewReviewMetricsService(),
	}
}

func (c Controller) ReviewMemberInfo(ctx context.Context, req *svcreview.ReviewMemberInfoReq) (result *svcreview.ReviewCommonResp, err error) {
	result = &svcreview.ReviewCommonResp{}
	// logger.Infof("ReviewMemberInfo %#v", req)
	var auditResult svcreview.AuditResult
	switch req.BizType {
	case svcreview.ReviewBizType_UserAlbum,
		svcreview.ReviewBizType_UserAvatar:
		auditResult, err = c.shumeiReviewMemberImageInfo(req)
	case svcreview.ReviewBizType_UserNickname,
		svcreview.ReviewBizType_UserTextSign:
		if len(req.Text) != 1 {
			result.Base = errcode.ErrorParam.ToSvcBaseResp()
			return
		}
		auditResult, err = c.shumeiReviewMemberTextInfo(req)
	case svcreview.ReviewBizType_UserSignVoice:
		if req.Voice == nil || len(req.Voice.Id) == 0 || len(req.Voice.Url) == 0 {
			result.Base = errcode.ErrorParam.ToSvcBaseResp()
			return
		}
		content := util.JsonStr(req)
		seqid, err := services.RecordReviewLog(req.Userid, review.BizTypeSignVoice, string(content), 0)
		if err != nil {
			logger.Errorf("error record review log %v", err)
			return nil, err
		}

		// 上报数美API调用指标
		contentType := c.metrics.GetContentTypeFromBizType(req.BizType)
		apiType := c.metrics.GetAPITypeFromBizType(req.BizType)

		var result shumei.AuditResultShumei
		result, err = shumei.AudioScan(req.Userid, req.Userid, req.Sex, 0, fmt.Sprintf("%d-%s", req.Userid, req.Voice.Id),
			req.Voice.Url, review.BizTypeSignVoice, "", &shumei.Origin{Content: content, SeqId: seqid})

		// 上报API调用结果（音频审核是异步的，这里只上报API调用）
		c.metrics.ReportShumeiAPICall(context.Background(), req.BizType, contentType, apiType, err == nil)

		if err == nil {
			auditResult = c.GetShumeiAuditResult(result)
		}

	default:
		result.Base = errcode.ErrorParam.ToSvcBaseResp()
		return
	}
	if err != nil {
		logger.Errorf("ReviewMemberInfo userid %v req %v err %v", req.Userid, util.JsonStr(req), err)
		result.Base = errcode.ErrorInternal.ToSvcBaseResp()
		return
	}
	return &svcreview.ReviewCommonResp{
		Base:   &common.SvcBaseResp{},
		Result: auditResult,
	}, nil

}

func (c Controller) reviewMemberCallFunc(seqid int64, param interface{}, result review.ReviewResult, details map[string]review.ReviewResult, isRobotJudge bool) (err error) {
	logger.Infof("reviewMemberCallFunc param:%v,result:%v,detail:%v", param, result, details)
	req, ok := param.(*svcreview.ReviewMemberInfoReq)
	if !ok {
		return
	}
	auditResult := c.GetAuditResult(result)
	r := &svcreview.ReviewResultDetail{
		Result: auditResult,
	}

	if isRobotJudge && auditResult == svcreview.AuditResult_pass { // 如果是机审核 即便是通过了，最终人审兜底
		return
	}

	if auditResult == svcreview.AuditResult_review {
		return
	}

	// 这两个有走数美同步审核 需要判断是否已拒绝
	if auditResult == svcreview.AuditResult_pass && (req.BizType == svcreview.ReviewBizType_UserNickname || req.BizType == svcreview.ReviewBizType_UserTextSign) {
		if services.GetReviewLogStatus(req.Userid, seqid) == int32(svcreview.AuditResult_reject) {
			return
		}
	}

	defer services.UpdateReviewLogStatus(req.Userid, seqid, int(auditResult))

	if req.BizType == svcreview.ReviewBizType_UserAlbum {
		for _, image := range req.Images {
			detailResult, ok := details[image.Id]
			if !ok {
				continue
			}
			imageResult := c.GetAuditResult(detailResult)
			r.Image = append(r.Image, &svcreview.ReviewImageResult{
				Image:  image,
				Result: imageResult,
				Reason: detailResult.Label,
			})
		}

	} else if req.BizType == svcreview.ReviewBizType_UserNickname ||
		req.BizType == svcreview.ReviewBizType_UserTextSign {
		r.Text = &svcreview.ReviewTextResult{
			Text:   req.Text[0],
			Result: auditResult,
			Reason: result.Label,
		}
	} else if req.BizType == svcreview.ReviewBizType_UserAvatar {
		r.Image = append(r.Image, &svcreview.ReviewImageResult{
			Image:  req.Images[0],
			Result: auditResult,
			Reason: result.Label,
		})
	} else if req.BizType == svcreview.ReviewBizType_UserSignVoice {
		r.Voice = &svcreview.ReviewVoiceResult{
			Voice:  req.Voice,
			Result: auditResult,
			Reason: result.Label,
		}
	}
	return c.r.CallbackMember(req.GetCallbackData(), r)
}

func (c Controller) shumeiReviewMemberTextInfo(req *svcreview.ReviewMemberInfoReq) (auditResult svcreview.AuditResult, err error) {
	bizType := ""
	switch req.BizType {
	case svcreview.ReviewBizType_UserNickname:
		bizType = review.BizTypeNickname
	case svcreview.ReviewBizType_UserTextSign:
		bizType = review.BizTypeLoveWords
	default:
		return
	}

	peerId := req.Userid
	if req.CallbackData != nil && req.CallbackData.Id != 0 {
		peerId = req.CallbackData.Id
	}

	// 上报数美API调用指标
	contentType := c.metrics.GetContentTypeFromBizType(req.BizType)
	apiType := c.metrics.GetAPITypeFromBizType(req.BizType)

	shumeiRes, err := shumei.TextScan(req.Userid, peerId, req.Sex, 0, req.Text[0], bizType, "", nil, false)

	// 上报API调用结果
	c.metrics.ReportShumeiAPICall(context.Background(), req.BizType, contentType, apiType, err == nil)

	if err != nil {
		return
	} else {
		auditResult = c.GetShumeiAuditResult(shumeiRes)

		// 记录增强的审核日志（包含风险原因）
		content := util.JsonStr(req)
		_, logErr := services.RecordReviewLogWithAuditResult(req.Userid, bizType, req.Userid, content, int(auditResult), &shumeiRes)
		if logErr != nil {
			logger.Errorf("error record enhanced review log %v", logErr)
		}

		// 上报同步审核结果
		resultStatus := c.metrics.GetResultStatusFromShumeiResult(auditResult)
		c.metrics.ReportSyncReviewResult(context.Background(), req.BizType, contentType, resultStatus, true)
	}
	return
}

func (c Controller) shumeiReviewMemberImageInfo(req *svcreview.ReviewMemberInfoReq) (auditResult svcreview.AuditResult, err error) {
	bizType := ""
	if len(req.Images) == 0 {
		return
	}

	switch req.BizType {
	case svcreview.ReviewBizType_UserAvatar:
		bizType = review.BizTypeAvatar
	case svcreview.ReviewBizType_UserAlbum:
		bizType = review.BizTypeAlbum
	case svcreview.ReviewBizType_Moment:
		bizType = review.BizTypeMoment
	default:
		return
	}
	peerId := req.Userid
	if req.CallbackData != nil && req.CallbackData.Id != 0 {
		peerId = req.CallbackData.Id
	}

	// 上报数美API调用指标
	contentType := c.metrics.GetContentTypeFromBizType(req.BizType)
	apiType := c.metrics.GetAPITypeFromBizType(req.BizType)

	url := req.Images[0].Url
	result, err := shumei.ImageScan(req.Userid, peerId, req.Sex, 0, url, bizType, "", nil)

	// 上报API调用结果
	c.metrics.ReportShumeiAPICall(context.Background(), req.BizType, contentType, apiType, err == nil)

	if err != nil {
		return
	}
	auditResult = c.GetShumeiAuditResult(result)

	// 记录增强的审核日志（包含风险原因）
	content := util.JsonStr(req)
	_, logErr := services.RecordReviewLogWithAuditResult(req.Userid, bizType, req.Userid, content, int(auditResult), &result)
	if logErr != nil {
		logger.Errorf("error record enhanced review log %v", logErr)
	}

	// 上报同步审核结果
	resultStatus := c.metrics.GetResultStatusFromShumeiResult(auditResult)
	c.metrics.ReportSyncReviewResult(context.Background(), req.BizType, contentType, resultStatus, true)

	return
}

// GetReviewLog 根据业务ID查询审核记录
func (c Controller) GetReviewLog(ctx context.Context, req *svcreview.GetReviewLogReq) (*svcreview.GetReviewLogResp, error) {
	result := &svcreview.GetReviewLogResp{
		Base: &common.SvcBaseResp{},
	}

	if req.Userid <= 0 || req.BizType == "" || req.BizId <= 0 {
		result.Base = errcode.ErrorParam.ToSvcBaseResp()
		return result, nil
	}

	reviewLog, err := services.GetReviewLogByBizId(req.Userid, req.BizType, req.BizId)
	if err != nil {
		logger.Errorf("GetReviewLog error: %v", err)
		result.Base = errcode.ErrorInternal.ToSvcBaseResp()
		return result, nil
	}

	if reviewLog != nil {
		result.Log = &svcreview.ReviewLogInfo{
			Id:          reviewLog.Id,
			Userid:      reviewLog.Userid,
			BizType:     reviewLog.BizType,
			BizId:       reviewLog.BizId,
			Data:        reviewLog.Data,
			Status:      int32(reviewLog.Status),
			RequestId:   reviewLog.RequestId,
			RiskReason:  reviewLog.RiskReason,
			RiskLabels:  reviewLog.RiskLabels,
			RiskLevel:   reviewLog.RiskLevel,
			AuditDetail: reviewLog.AuditDetail,
			Ct:          reviewLog.Ct,
			Ut:          reviewLog.Ut,
		}
	}

	result.Base = errcode.ErrOK.ToSvcBaseResp()
	return result, nil
}

// GetReviewLogs 查询审核记录列表
func (c Controller) GetReviewLogs(ctx context.Context, req *svcreview.GetReviewLogsReq) (*svcreview.GetReviewLogsResp, error) {
	result := &svcreview.GetReviewLogsResp{
		Base: &common.SvcBaseResp{},
	}

	if req.Userid <= 0 {
		result.Base = errcode.ErrorParam.ToSvcBaseResp()
		return result, nil
	}

	// 设置默认分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	limit := int(pageSize)

	var reviewLogs []model.ReviewLog
	var total int64
	var err error

	// 根据查询条件选择不同的查询方法
	if req.BizType != "" {
		reviewLogs, total, err = services.GetReviewLogsByBizType(req.Userid, req.BizType, limit, int(offset))
	} else if req.RiskLevel != "" {
		reviewLogs, total, err = services.GetReviewLogsByRiskLevel(req.Userid, req.RiskLevel, limit, int(offset))
	} else {
		// 如果没有指定条件，返回参数错误
		result.Base = errcode.ErrorParam.ToSvcBaseResp()
		return result, nil
	}

	if err != nil {
		logger.Errorf("GetReviewLogs error: %v", err)
		result.Base = errcode.ErrorInternal.ToSvcBaseResp()
		return result, nil
	}

	// 转换结果
	result.Logs = make([]*svcreview.ReviewLogInfo, 0, len(reviewLogs))
	for _, log := range reviewLogs {
		result.Logs = append(result.Logs, &svcreview.ReviewLogInfo{
			Id:          log.Id,
			Userid:      log.Userid,
			BizType:     log.BizType,
			BizId:       log.BizId,
			Data:        log.Data,
			Status:      int32(log.Status),
			RequestId:   log.RequestId,
			RiskReason:  log.RiskReason,
			RiskLabels:  log.RiskLabels,
			RiskLevel:   log.RiskLevel,
			AuditDetail: log.AuditDetail,
			Ct:          log.Ct,
			Ut:          log.Ut,
		})
	}

	result.Total = total
	result.Base = errcode.ErrOK.ToSvcBaseResp()
	return result, nil
}
